<?php

namespace App\Livewire\Dashboard;

use App\Model\Dashboards\Dashboard;
use App\Model\Pool;
use App\Model\Analyse as Analysis;
use Livewire\Component;
use Livewire\WithFileUploads;
use Illuminate\Validation\Rule;
use Illuminate\Support\Facades\Storage;
use App\Traits\LivewireGeneralFunctions;
use App\Services\Dashboard\WidgetViewService;

class EditWidget extends Component
{
    use WithFileUploads, LivewireGeneralFunctions;

    public $dashboardId;
    public $widgetId;
    public $widget; // This now holds the full widget array from dashboard_data

    // Public properties bound to form inputs
    public $widget_title;
    public $pool_type;
    public $diagram_type;
    public $chakra_image; // This will hold the uploaded file object
    public $exist_chakra_image; // This will hold the name/path of the existing image on storage
    public $image_position;
    public $image_max_width = 50; // Default to 50% max width
    public $sorted_ids = [];
    public $richTextContent = ''; // Holds the content for rich text editor

    // Livewire property for sidebar collapsed state
    public $sidebarCollapsed = false; // Default to open (not collapsed)
    public $activeTab = 'settings';

    // Livewire properties for pool search functionality
    public $searchPoolTerm = '';
    public $showPoolDropdown = false;
    public $selectedPoolName = '';
    public $pools;
    public $analyses = [];

    // Preview data
    public $previewData = null;
    public $temporaryImageUrl = null; // Holds the temporary URL for newly uploaded image preview


    // Updated: Listener for sorting update and new sidebar listener
    protected $listeners = [
        'updateSortedIdsLive', // Keeps component's property in sync with UI sorting
        'setSidebarCollapsedState', // New listener to set sidebar state from JS
    ];

    /**
     * Mounts the component, initializing properties from the existing widget data.
     *
     * @param int $dashboardId
     * @param string $widgetId
     * @return void
     */
    public function mount(int $dashboardId, string $widgetId): void
    {
        $this->dashboardId = $dashboardId;
        $this->widgetId = $widgetId;

        $dashboard = Dashboard::findOrFail($this->dashboardId);
        $this->widget = collect($dashboard->dashboard_data ?? [])->firstWhere('id', $this->widgetId);

        if (!$this->widget) {
            abort(404, 'Widget not found.');
        }

        // Populate form fields with existing widget data
        $this->widget_title = $this->widget['title'] ?? '';
        $this->pool_type = $this->widget['pool_type'] ?? null;
        $this->diagram_type = $this->widget['diagram_type'] ?? '';
        $this->exist_chakra_image = $this->widget['settings']['image'] ?? null;
        $this->image_position = $this->widget['settings']['image_position'] ?? 'right';
        $this->image_max_width = $this->widget['settings']['image_max_width'] ?? 50;
        $this->sorted_ids = $this->widget['settings']['sorting'] ?? [];
        $this->richTextContent = $this->widget['settings']['content'] ?? '';
        $this->chakra_image = $this->widget['settings']['image'] ?? '';

        // Initialize pool search related properties
        $this->loadPools();
        if ($this->pool_type) {
            $currentPool = $this->pools->firstWhere('id', $this->pool_type);
            if ($currentPool) {
                $this->selectedPoolName = $currentPool->pool_name;
            }
        }
        $this->loadAnalyses();

        // Load initial preview data
        $this->loadPreviewData();
    }

    /**
     * Toggles the sidebar collapsed state and dispatches event to update localStorage.
     * @return void
     */
    public function toggleSidebarState(): void
    {
        $this->sidebarCollapsed = !$this->sidebarCollapsed;
        // Dispatch event to client-side JS to update localStorage
        $this->dispatch('updateLocalStorageSidebar', ['collapsed' => $this->sidebarCollapsed]);
    }

    /**
     * Listener to set the sidebar collapsed state from client-side JavaScript.
     * @param bool $collapsed
     * @return void
     */
    public function setSidebarCollapsedState(bool $collapsed): void
    {
        $this->sidebarCollapsed = $collapsed;
    }

    /**
     * Loads preview data for the widget using WidgetViewService
     * @return void
     */
    public function loadPreviewData(): void
    {
        // Only attempt to load preview if necessary fields are set
        if (!$this->pool_type || !$this->diagram_type) {
            $this->previewData = null;
            return;
        }

        try {
            $widgetService = app(WidgetViewService::class);
            // Ensure sorting IDs are integer array for the service
            $sortingForService = empty($this->sorted_ids) ? [] : array_map('intval', $this->sorted_ids);

            // Determine which image to use for preview
            $currentPreviewImage = null;
            if ($this->temporaryImageUrl) {
                $currentPreviewImage = $this->temporaryImageUrl; // New upload takes precedence (temporary URL)
            } elseif ($this->exist_chakra_image) {
                // For existing image, generate a URL for preview if it's not already a URL
                $currentPreviewImage = Storage::url('images/' . $this->exist_chakra_image);
            }

            // Build preview widget structure, using original width/height from dashboard_data
            $previewWidget = [
                'id' => $this->widgetId . '-preview-' . uniqid(), // Use uniqid to force re-render
                'title' => $this->widget_title,
                'pool_type' => $this->pool_type,
                'diagram_type' => $this->diagram_type,
                'width' => $this->widget['width'] ?? 4, // Use original widget width
                'height' => $this->widget['height'] ?? 3, // Use original widget height
                'settings' => [
                    'sorting' => $sortingForService,
                    'image' => $currentPreviewImage, // Pass the chosen image path/URL for preview
                    'image_position' => $this->image_position
                ]
            ];

            // Get data from service for graph widgets
            if (in_array(strtolower($this->diagram_type), ['bar', 'line', 'radar', 'polararea', 'progress','progressbarsmall'])) {
                $result = $widgetService->processWidgetView($this->pool_type, $this->diagram_type, [], $sortingForService);
                $previewWidget['data'] = $result;
            }

            $this->previewData = $previewWidget;
        } catch (\Exception $e) {
            // Log the exception for debugging
            \Log::error("Error loading preview data: " . $e->getMessage());
            $this->previewData = null;
            $this->showToastr('error', 'Preview Error', 'Could not load preview. Please check settings.');
        }
    }

    /**
     * Loads all available pools from the database, filtered by search term.
     * @return void
     */
    public function loadPools(): void
    {
        $query = Pool::select('id', 'pool_name')->orderBy('pool_name');

        if (!empty($this->searchPoolTerm)) {
            $query->where('pool_name', 'like', '%' . $this->searchPoolTerm . '%');
        }

        $this->pools = $query->get();
    }

    /**
     * Loads analyses based on the current pool_type and sorting order using WidgetViewService.
     * @return void
     */
    public function loadAnalyses(): void
    {
        if (is_null($this->pool_type)) {
            $this->analyses = [];
            return;
        }

        // Instantiate the service
        $widgetService = app(WidgetViewService::class);

        // Determine the sorting array to pass to the service
        $sortingForService = empty($this->sorted_ids) ? [] : array_map('intval', $this->sorted_ids);

        // Fetch analyses using the service
        // Using 'progress' as a placeholder diagram type for fetching analyses names
        $result = $widgetService->processWidgetView($this->pool_type, 'progress', [], $sortingForService);

        // Decode the JSON string from $result['results']
        $decodedResults = $result['results'] ?? [];

        // Map the decoded results into the [id => name] format expected by the Blade view
        $formattedAnalyses = [];
        if (is_array($decodedResults)) {
            foreach ($decodedResults as $analysisData) {
                if (isset($analysisData['analysis_id']) && isset($analysisData['name'])) {
                    $formattedAnalyses[$analysisData['analysis_id']] = $analysisData['name'];
                }
            }
        }

        $this->analyses = $formattedAnalyses;
    }

    /**
     * Hook that runs when 'searchPoolTerm' property is updated (via wire:model.live).
     * Reloads pools and shows the dropdown.
     * @return void
     */
    public function updatedSearchPoolTerm(): void
    {
        $this->loadPools();
        $this->showPoolDropdown = true;
    }

    /**
     * Hook that runs when any form field is updated to refresh the preview
     * @return void
     */
    public function updated($propertyName): void
    {
        // List of properties that should trigger preview update
        $previewProperties = ['widget_title', 'diagram_type', 'image_position'];

        if (in_array($propertyName, $previewProperties)) {
            $this->loadPreviewData();
        }
    }

    /**
     * Hook that runs when 'pool_type' property is updated.
     * Resets the sorting and reloads analyses for the new pool.
     * @return void
     */
    public function updatedPoolType(): void
    {
        $this->sorted_ids = []; // Reset sorting when pool changes
        $this->loadAnalyses();
        $this->loadPreviewData();

        // Dispatch event for client-side sorting re-initialization
        $this->dispatch('poolChanged');
    }

    /**
     * Hook that runs when 'chakra_image' property is updated.
     * Creates a temporary URL for preview or clears it if no file is selected.
     * @return void
     */
    public function updatedChakraImage(): void
    {
        // Validate the image only if one is actually provided
        if ($this->chakra_image) {
            $this->validateOnly('chakra_image', [
                'chakra_image' => 'nullable|image|mimes:jpg,jpeg,png|max:200',
            ]);

            try {
                $this->temporaryImageUrl = $this->chakra_image->temporaryUrl();
            } catch (\Exception $e) {
                $this->temporaryImageUrl = null;
                $this->showToastr('error', 'Image Error', 'Could not generate temporary image URL.');
                \Log::error('Error generating temporary image URL: ' . $e->getMessage());
            }
        } else {
            // If chakra_image becomes null (e.g., user cancels file selection), clear temporary URL
            $this->temporaryImageUrl = null;
        }

        // Always call loadPreviewData after image changes to refresh the preview
        $this->loadPreviewData();
    }

    /**
     * Selects a pool from the search results.
     * @param int $poolId
     * @param string $poolName
     * @return void
     */
    public function selectPool(int $poolId, string $poolName): void
    {
        // Only update if the selected pool is different
        if ((string)$this->pool_type !== (string)$poolId) {
            $this->pool_type = $poolId;
            $this->selectedPoolName = $poolName;
            $this->searchPoolTerm = ''; // Clear search term after selection
            $this->updatedPoolType(); // Manually trigger updatedPoolType logic
        }
        $this->showPoolDropdown = false; // Close dropdown after selection
    }

    /**
     * Toggles the visibility of the pool dropdown.
     * @return void
     */
    public function togglePoolDropdown(): void
    {
        $this->showPoolDropdown = !$this->showPoolDropdown;
        if ($this->showPoolDropdown && empty($this->searchPoolTerm)) {
            $this->loadPools(); // Load all pools when opening if no search term
        }
        // Dispatch event to focus the search input
        if ($this->showPoolDropdown) {
            $this->dispatch('poolDropdownOpened');
        }
    }

    /**
     * Closes the pool dropdown.
     * Used with @click.away.
     * @return void
     */
    public function closePoolDropdown(): void
    {
        $this->showPoolDropdown = false;
    }

    /**
     * Listener to update sorted_ids property immediately after a sort event in the UI.
     * This does NOT trigger a save, only synchronizes the component's state with the UI.
     * @param array $sortedIds The new order of analysis IDs.
     * @return void
     */
    public function updateSortedIdsLive(array $sortedIds): void
    {
        $this->sorted_ids = $sortedIds;

        // Update the analyses array to reflect the new order without re-querying the database
        if (!empty($this->analyses) && !empty($sortedIds)) {
            $reorderedAnalyses = [];
            foreach ($sortedIds as $id) {
                if (isset($this->analyses[$id])) {
                    $reorderedAnalyses[$id] = $this->analyses[$id];
                }
            }
            // Add any analyses that weren't in sortedIds (should ideally not happen, but for robustness)
            foreach ($this->analyses as $id => $name) {
                if (!isset($reorderedAnalyses[$id])) {
                    $reorderedAnalyses[$id] = $name;
                }
            }
            $this->analyses = $reorderedAnalyses;
        }

        // Force preview refresh after sorting change
        $this->loadPreviewData();
    }

    /**
     * Saves the widget data to the dashboard.
     * @return void
     */
    public function saveWidget()
    {
        $validate = [
            'widget_title' => 'required|string|max:255',
            'pool_type' => 'required|exists:pools,id',
            'diagram_type' => 'required|string',
            'image_position' => ['nullable', Rule::in(['left', 'right'])],
            'image_max_width' => 'nullable|integer|min:20|max:80',
        ];


        if($this->chakra_image !== $this->exist_chakra_image) {
            $validate['chakra_image'] = 'nullable|image|mimes:jpg,jpeg,png|max:200';
        }

        $this->validate($validate);

        $dashboard = Dashboard::findOrFail($this->dashboardId);
        $widgets = $dashboard->dashboard_data ?? [];

        // Handle image upload
        if ($this->chakra_image != $this->exist_chakra_image) {
            // Delete old image if it exists. $this->exist_chakra_image should contain the full path.
            if ($this->exist_chakra_image && Storage::exists($this->exist_chakra_image)) {
                Storage::delete($this->exist_chakra_image);
            }
            // Store new image and get the full path (e.g., 'images/new_filename.jpg')
            $this->exist_chakra_image = $this->chakra_image->store('widgets', 'images');
            $this->chakra_image = $this->exist_chakra_image;

        } elseif ($this->chakra_image === null && $this->temporaryImageUrl === null && $this->exist_chakra_image) {
            // This condition handles explicit removal of an image
            if (Storage::exists($this->exist_chakra_image)) {
                Storage::delete($this->exist_chakra_image);
            }
            $this->exist_chakra_image = null;
        }


        // Find the widget and update its data
        $updatedWidgets = collect($widgets)->map(function ($item) {
            if ($item['id'] === $this->widgetId) {
                $item['title'] = $this->widget_title;
                $item['pool_type'] = $this->pool_type;
                $item['diagram_type'] = $this->diagram_type;
                $item['settings']['sorting'] = $this->sorted_ids;
                $item['settings']['image'] = $this->exist_chakra_image;
                $item['settings']['image_position'] = $this->image_position;
                $item['settings']['image_max_width'] = $this->image_max_width;

                // Ensure width and height are preserved from the original widget data
                // as they are no longer editable in this form.
                $item['width'] = $this->widget['width'] ?? 4;
                $item['height'] = $this->widget['height'] ?? 3;
            }
            return $item;
        })->all();

        $dashboard->dashboard_data = $updatedWidgets;
        $dashboard->save();
        $this->temporaryImageUrl = null; // Clear the temporary URL

        $this->showToastr('success', 'Success', 'Widget updated successfully.');

        // Dispatch an event to the frontend to signal successful save
        $this->dispatch('widgetSaved');
    }

    public function saveContent()
    {
        $dashboard = Dashboard::findOrFail($this->dashboardId);
        $widgets = $dashboard->dashboard_data ?? [];
        $updatedWidgets = collect($widgets)->map(function ($item) {
            if ($item['id'] === $this->widgetId) {
                // Update the content field in the widget settings
                $item['settings']['content'] = $this->richTextContent;
                return $item;
            }

            return $item;
        })->all();

        $dashboard->dashboard_data = $updatedWidgets;
        $dashboard->save();

        $this->showToastr('success', 'Success', 'Content saved successfully.');
    }

    /**
     * Renders the Livewire component view.
     * @return \Illuminate\View\View
     */
    public function render()
    {
        return view('livewire.dashboard.edit-widget');
    }
}