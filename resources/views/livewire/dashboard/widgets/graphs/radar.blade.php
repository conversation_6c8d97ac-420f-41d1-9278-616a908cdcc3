<div>
    @php
        $itemCount = count($fullResults);
    @endphp
    <div id="{{$uniqueId}}_polar_chart_container" class="chart-container-wrapper">
        <div class="chart-container" id="{{$uniqueId}}_container">
            <div class="chart-wrapper">
                <canvas id="{{$uniqueId}}_module_head_chart" class="{{$diagramType}}" style="width: 150px; height: 150px; max-width: 150px; max-height: 150px;"></canvas>
            </div>
            <div class="radial-legend">
                @foreach($fullResults as $index => $item)
                    @php
                        $angle = 0;
                        if ($itemCount > 0) {
                            $maxAngle = 130;
                            $power = 0.7;
                            if ($itemCount == 1) {
                                $angle = 0;
                            } else {
                                $normalizedPosition = ($index / ($itemCount - 1)) * 2 - 1;
                                $sign = ($normalizedPosition > 0) - ($normalizedPosition < 0);
                                $curvedPosition = pow(abs($normalizedPosition), $power);
                                $angle = $sign * $curvedPosition * $maxAngle;
                            }
                        }
                        $radius = '135px';
                    @endphp
                    <div class="legend-item" style="--angle: {{ $angle }}deg; --radius: {{ $radius }};">
                        <div class="legend-label" title="{{ $item['name'] }}">{{ $item['name'] }}</div>
                        <div class="legend-value" style="color: {{ $item['color'] }};">{{ $item['val'] }}</div>
                        @if(!empty($item['description']) || !empty($item['desc_img']) || !empty($item['bodyDesc']) || !empty($item['mentalDesc']))
                            <span class="btn-legend btn-info"
                                  data-poolid="{{ $poolId ?? '' }}"
                                  data-analysisid="{{ $item['analysis_id'] }}"
                                  data-container="body"
                                  data-toggle="popover"
                                  data-placement="bottom"
                                  data-html="true"
                                  data-content="">
                                <i class="fas fa-info-circle"></i>
                            </span>
                        @endif
                        <div class="add-to-cart-container">
                            <livewire:dashboard.single-analyses-add-to-cart
                                    :wire:key="'chart-add-to-cart-' . $item['analysis_id']"
                                    :poolId="$poolId"
                                    :analysesId="$item['analysis_id']"
                            />
                        </div>
                    </div>
                @endforeach
            </div>
        </div>
    </div>
    {{-- Popover içerikleri için gizli div'ler --}}
    @if($allowInfo ?? true)
        <div class="d-none">
            @foreach($fullResults as $item)
                <div id="popover-content-{{ $poolId ?? '' }}-{{ $item['analysis_id'] }}" class="hide">
                    @if(!empty($item['description']))
                        <h6>{{__('action.main_desc')}}</h6>
                        <p>{!! $item['description'] !!}</p>
                    @endif
                    @if(isset($item['desc_img']))
                        <img loading="lazy" src="{{ asset('/storage/analyse/images/description') }}/{{ $item['desc_img'] }}" class="popover-showimg" alt="" height="250px" width="auto">
                    @endif
                    <hr>
                    @if(!empty($item['bodyDesc']))
                        <h6>{{__('action.body')}}</h6>
                        <p>{!! $item['bodyDesc'] !!}</p>
                    @endif
                    <hr>
                    @if(!empty($item['mentalDesc']))
                        <h6>{{__('action.mental')}}</h6>
                        <p>{!! $item['mentalDesc'] !!}</p>
                    @endif
                </div>
            @endforeach
        </div>
    @endif
</div>
@assets
<script src="{{ asset('/js/chart/chart.min.js') }}"></script>
<script src="{{ asset('/js/chart/chartjs-plugin-annotation.min.js') }}"></script>
<style>
    .chart-container-wrapper {
        position: relative;
        width: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
    }
    .chart-container-wrapper .add-to-cart-container {
        line-height: 1;
        margin-left: 2px;
    }
    .chart-container-wrapper .add-to-cart-container .cart-wrapper {
        transform: none !important;
        margin-left: 0 !important;
    }
    .chart-container-wrapper .chart-container {
        position: relative;
        height: 300px;
        width: 100%;
        max-width: 400px;
    }
    .chart-container-wrapper .chart-container .chart-wrapper {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 150px;
        height: 150px;
    }
    .chart-container-wrapper .radial-legend {
        position: absolute;
        width: 100%;
        height: 100%;
        pointer-events: none;
    }
    .chart-container-wrapper .legend-item {
        position: absolute;
        display: flex;
        align-items: center;
        pointer-events: auto;
        font-size: 12px;
        white-space: nowrap;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%) rotate(var(--angle)) translateY(var(--radius)) rotate(calc(-1 * var(--angle)));
    }
    .chart-container-wrapper .legend-label {
        font-weight: 600;
        color: #2c3e50;
        margin-right: 6px;
        font-size: 11px;
        max-width: 120px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }
    .chart-container-wrapper .legend-value {
        font-weight: bold;
        font-size: 12px;
        text-align: center;
    }
    .chart-container-wrapper .btn-legend {
        border: none;
        cursor: pointer;
        font-weight: 500;
        margin-left: 4px;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    .chart-container-wrapper .btn-legend.btn-info {
        background: inherit;
        color: inherit;
    }
    .chart-container-wrapper .btn-legend.btn-info:hover {
        background-color: inherit;
        background: inherit;
        color: inherit;
    }
    .chart-container-wrapper .btn-info .fas.fa-info-circle {
        font-size: 14px;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .chart-container-wrapper .chart-container {
            height: 250px;
            max-width: 300px;
        }
        .chart-container-wrapper .chart-container .chart-wrapper {
            width: 120px;
            height: 120px;
        }
        .chart-container-wrapper .chart-container .chart-wrapper canvas {
            width: 120px !important;
            height: 120px !important;
            max-width: 120px !important;
            max-height: 120px !important;
        }
        .chart-container-wrapper .legend-item {
            font-size: 10px;
        }
        .chart-container-wrapper .legend-label {
            font-size: 9px;
            max-width: 80px;
        }
        .chart-container-wrapper .legend-value {
            font-size: 10px;
        }
    }

    @media (max-width: 480px) {
        .chart-container-wrapper .chart-container {
            height: 200px;
            max-width: 250px;
        }
        .chart-container-wrapper .chart-container .chart-wrapper {
            width: 100px;
            height: 100px;
        }
        .chart-container-wrapper .chart-container .chart-wrapper canvas {
            width: 100px !important;
            height: 100px !important;
            max-width: 100px !important;
            max-height: 100px !important;
        }
        .chart-container-wrapper .legend-item {
            font-size: 9px;
        }
        .chart-container-wrapper .legend-label {
            font-size: 8px;
            max-width: 60px;
        }
        .chart-container-wrapper .legend-value {
            font-size: 9px;
        }
    }
</style>
@endassets

@script
<script>
    let id = @js($uniqueId);
    let diagram_type = @js($diagramType);
    let filter = @js($filter);
    let results = @json($results);
    let labels = @json($labels);
    let fullResults = @json($fullResults);

    function initializeLegendTooltips() {
        $('.legend-label[title]').each(function() {
            const element = $(this);
            const textWidth = element[0].scrollWidth;
            const containerWidth = element.width();
            if (textWidth > containerWidth) {
                element.tooltip({
                    placement: 'top',
                    trigger: 'hover',
                    delay: { show: 300, hide: 100 }
                });
            }
        });
    }

    function createRadarChart(id, labels, datasets, filters) {
        const formattedDatasets = getFormattedDatasets(datasets, filters);
        const config = {
            type: 'radar',
            data: {
                labels: labels.map(label => label.split(':')[0]),
                datasets: formattedDatasets
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: { display: false },
                    tooltip: {
                        callbacks: {
                            title: function(context) {
                                // Show full label in tooltip
                                return labels[context[0].dataIndex] || context[0].label;
                            }
                        }
                    },
                    zoom: {
                        pan: { enabled: true, mode: 'xy' },
                        zoom: { enabled: true, mode: 'xy' },
                    },
                },
                scales: {
                    r: {
                        beginAtZero: true,
                        ticks: {
                            stepSize: 10,
                            reverse: false,
                            display: false
                        },
                        pointLabels: {
                            display: false,
                            centerPointLabels: true,
                            font: { size: 16 }
                        },
                    }
                }
            }
        };

        const canvasId = `${id}_module_head_chart`;
        const canvas = document.getElementById(canvasId);
        if (!canvas) return;
        const ctx = canvas.getContext('2d');

        if (window.chartInstances && window.chartInstances[canvasId]) {
            window.chartInstances[canvasId].destroy();
        }

        window.chartInstances = { ...(window.chartInstances || {}), [canvasId]: new Chart(ctx, config) };
    }

    function getFormattedDatasets(datasets, filters) {
        const createDataset = (data, index = 0) => {
            const chartColors = getChartColors(data);
            const isPrimary = index === 0;

            return {
                data,
                fill: true,
                backgroundColor: filters
                    ? (isPrimary ? 'rgba(255, 99, 132, 0.2)' : 'rgba(75, 192, 192, 0.2)')
                    : 'rgba(54, 162, 235, 0.2)',
                borderColor: filters
                    ? (isPrimary ? 'rgba(255, 99, 132, 1)' : 'rgba(47, 171, 102, 1)')
                    : 'rgba(54, 162, 235, 1)',
                pointBackgroundColor: chartColors.backgroundColor,
                pointBorderColor: chartColors.borderColor,
                pointHoverBackgroundColor: chartColors.borderColor,
                pointHoverBorderColor: chartColors.backgroundColor,
                pointRadius: 5,
                borderWidth: 2
            };
        };

        return filters
            ? datasets.map((data, index) => createDataset(data, index))
            : [createDataset(datasets)];
    }


    function getChartColors(dataSet) {
        const getColor = value => {
            if (value <= 10) return ['rgba(232, 78, 27, 1)', 'rgb(232, 78, 27)'];
            if (value <= 69) return ['rgba(248, 177, 51, 1)', 'rgb(248, 177, 51)'];
            if (value <= 100) return ['rgba(47, 171, 102, 1)', 'rgb(47, 171, 102)'];
            return ['rgba(0, 0, 0, 0.2)', 'rgb(0, 0, 0)'];
        };

        return dataSet.reduce((colors, value) => {
            const [bgColor, borderColor] = getColor(value);
            colors.backgroundColor.push(bgColor);
            colors.borderColor.push(borderColor);
            return colors;
        }, { backgroundColor: [], borderColor: [] });
    }

    // Initial chart render
    createRadarChart(id, labels, results, filter);

    $(function ($) {
        $('[data-toggle="popover"]').each(function () {
            var popoverTrigger = $(this);
            var poolId = popoverTrigger.data('poolid');
            var analysisId = popoverTrigger.data('analysisid');
            var popoverContent = $('#popover-content-' + poolId + '-' + analysisId);
            popoverTrigger.popover({
                html: true,
                content: function () {
                    return popoverContent.html();
                }
            });
        });

        initializeLegendTooltips();
    });

    $('body').on('click', function (e) {
        $('[data-toggle=popover]').each(function () {
            if (!$(this).is(e.target) && $(this).has(e.target).length === 0 && $('.popover').has(e.target).length === 0) {
                $(this).popover('hide');
            }
        });
    });
</script>
@endscript